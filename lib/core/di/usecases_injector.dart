import 'package:get_it/get_it.dart';
import 'package:mcdc/features/user/domain/usecases/check_duplicate_id_card.dart';
import 'package:mcdc/features/user/domain/usecases/check_login_status.dart';
import 'package:mcdc/features/user/domain/usecases/member_login.dart';
import 'package:mcdc/features/user/domain/usecases/member_logout.dart';
import 'package:mcdc/features/user/domain/usecases/change_password.dart';
import 'package:mcdc/features/user/domain/usecases/delete_account.dart';
import 'package:mcdc/features/poll/domain/usecases/get_feedback_questions.dart';
import 'package:mcdc/features/user/domain/usecases/get_user_profile.dart';
import 'package:mcdc/features/poll/domain/usecases/submit_feedback.dart';
import 'package:mcdc/features/user/domain/usecases/update_user_profile.dart';
import 'package:mcdc/features/user/domain/usecases/validate_member_register.dart';
import 'package:mcdc/features/user/domain/usecases/member_register.dart';
import 'package:mcdc/features/otp/domain/usecases/send_otp_usecase.dart';
import 'package:mcdc/features/otp/domain/usecases/verify_otp_usecase.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_member_types.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_government_sectors.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_ministries_by_government_sector.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_departments_by_ministry.dart';

/// Initialize all use cases used in the application
void initUseCases(GetIt sl) {
  // User Use Cases
  sl.registerLazySingleton(() => MemberLogin(sl(), sl()));
  sl.registerLazySingleton(() => MemberLogout(sl()));
  sl.registerLazySingleton(() => CheckLoginStatus(sl()));
  sl.registerLazySingleton(() => CheckDuplicateIdCard(sl()));
  sl.registerLazySingleton(() => ValidateMemberRegister(sl()));
  sl.registerLazySingleton(() => MemberRegister(sl(), sl()));
  // Feedback Use Cases
  sl.registerLazySingleton(() => GetFeedbackQuestions(sl()));
  sl.registerLazySingleton(() => SubmitFeedback(sl()));

  // User Profile Use Cases
  sl.registerLazySingleton(() => GetUserProfile(sl()));
  sl.registerLazySingleton(() => UpdateUserProfile(sl()));
  sl.registerLazySingleton(() => ChangePassword(sl()));
  sl.registerLazySingleton(() => DeleteAccount(sl()));

  // OTP Use Cases
  sl.registerLazySingleton(() => SendOtpUseCase(sl()));
  sl.registerLazySingleton(() => VerifyOtpUseCase(sl()));

  // Master Data Use Cases
  sl.registerLazySingleton(() => GetMemberTypes(sl()));
  sl.registerLazySingleton(() => GetGovernmentSectors(sl()));
  sl.registerLazySingleton(() => GetMinistriesByGovernmentSector(sl()));
  sl.registerLazySingleton(() => GetDepartmentsByMinistry(sl()));
}
