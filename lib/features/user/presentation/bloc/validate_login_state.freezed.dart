// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'validate_login_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ValidateLoginState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidateLoginState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ValidateLoginState()';
}


}

/// @nodoc
class $ValidateLoginStateCopyWith<$Res>  {
$ValidateLoginStateCopyWith(ValidateLoginState _, $Res Function(ValidateLoginState) __);
}


/// @nodoc


class ValidateLoginIdle implements ValidateLoginState {
  const ValidateLoginIdle();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidateLoginIdle);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ValidateLoginState.idle()';
}


}




/// @nodoc


class ValidateLoginLoading implements ValidateLoginState {
  const ValidateLoginLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidateLoginLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ValidateLoginState.loading()';
}


}




/// @nodoc


class ValidateLoginValid implements ValidateLoginState {
  const ValidateLoginValid();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidateLoginValid);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ValidateLoginState.valid()';
}


}




/// @nodoc


class ValidateLoginInvalid implements ValidateLoginState {
  const ValidateLoginInvalid(this.error);
  

 final  LoginValidationError error;

/// Create a copy of ValidateLoginState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ValidateLoginInvalidCopyWith<ValidateLoginInvalid> get copyWith => _$ValidateLoginInvalidCopyWithImpl<ValidateLoginInvalid>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidateLoginInvalid&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,error);

@override
String toString() {
  return 'ValidateLoginState.invalid(error: $error)';
}


}

/// @nodoc
abstract mixin class $ValidateLoginInvalidCopyWith<$Res> implements $ValidateLoginStateCopyWith<$Res> {
  factory $ValidateLoginInvalidCopyWith(ValidateLoginInvalid value, $Res Function(ValidateLoginInvalid) _then) = _$ValidateLoginInvalidCopyWithImpl;
@useResult
$Res call({
 LoginValidationError error
});


$LoginValidationErrorCopyWith<$Res> get error;

}
/// @nodoc
class _$ValidateLoginInvalidCopyWithImpl<$Res>
    implements $ValidateLoginInvalidCopyWith<$Res> {
  _$ValidateLoginInvalidCopyWithImpl(this._self, this._then);

  final ValidateLoginInvalid _self;
  final $Res Function(ValidateLoginInvalid) _then;

/// Create a copy of ValidateLoginState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? error = null,}) {
  return _then(ValidateLoginInvalid(
null == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as LoginValidationError,
  ));
}

/// Create a copy of ValidateLoginState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LoginValidationErrorCopyWith<$Res> get error {
  
  return $LoginValidationErrorCopyWith<$Res>(_self.error, (value) {
    return _then(_self.copyWith(error: value));
  });
}
}

// dart format on
